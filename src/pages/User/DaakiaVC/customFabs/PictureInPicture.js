import { useRef, useState, useCallback, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';
import { VideoTrack, useTracks, isTrackReference, useParticipants, TrackToggle, DisconnectButton } from '@livekit/components-react';
import { generateAvatar, parseMetadata } from '../utils/helper';
import { useAutomaticPiPContext } from '../context/indexContext';
// import { ScreenCaptureButton } from '../components/settings/ScreenCapture/ScreenCaptureButton';
import '../styles/PictureInPicture.scss';
import { ReactComponent as EndCallIconPip } from './icons/endcalliconPip.svg';

// Speaking priority configuration
const SPEAKING_PRIORITY_CONFIG = {
  STICKY_DURATION: 5000, // Keep speaking participants visible for 5 seconds after they stop speaking
  UPDATE_INTERVAL: 500,   // Check for speaking changes every 500ms
};

function SimplePipContent({
  localParticipant,
  controlPosition = 'bottom'
}) {

  const allParticipants = useParticipants();

  // State for speaking priority tracking
  const [speakingHistory, setSpeakingHistory] = useState(new Map());



  // Get camera and screen share tracks for video display
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
    { source: Track.Source.ScreenShare, withPlaceholder: false },
  ]);

  // Find local camera track
  const localCameraTrack = allTracks
    .filter(isTrackReference)
    .find((track) =>
      track.participant.isLocal &&
      track.source === Track.Source.Camera
    );


  const screenShareTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => track.publication.source === Track.Source.ScreenShare);


  const isScreenShareActive = screenShareTracks.some((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );

  const activeScreenShareTrack = screenShareTracks.find((track) =>
    track.publication.isSubscribed && !track.publication.isMuted
  );


  useEffect(() => {
    const now = Date.now();
    const currentSpeaking = allParticipants.filter(p => p.isSpeaking);

    if (currentSpeaking.length > 0) {
      setSpeakingHistory(prev => {
        const updated = new Map(prev);
        currentSpeaking.forEach(p => {
          updated.set(p.identity, now);
        });
        return updated;
      });
    }
  }, [allParticipants.map(p => `${p.identity}-${p.isSpeaking}`).join(',')]);

  // Advanced Speaking Priority System with Sticky Behavior
  const getAdvancedSpeakingPriorityParticipants = useCallback((participants, maxCount) => {
    const now = Date.now();
    const currentSpeaking = participants.filter(p => p.isSpeaking);
    const currentSpeakingIds = new Set(currentSpeaking.map(p => p.identity));

    // Get recently speaking participants (within sticky duration)
    const recentlySpeaking = participants.filter(p => {
      const lastSpeakingTime = speakingHistory.get(p.identity);
      return lastSpeakingTime && (now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION;
    });

    // Priority order: Currently speaking > Recently speaking > Others
    const currentlySpakingParticipants = participants.filter(p => currentSpeakingIds.has(p.identity));
    const recentlySpokingParticipants = recentlySpeaking.filter(p => !currentSpeakingIds.has(p.identity));
    const otherParticipants = participants.filter(p =>
      !currentSpeakingIds.has(p.identity) &&
      !recentlySpeaking.some(rp => rp.identity === p.identity)
    );

    // Build final list with priority
    const prioritizedList = [
      ...currentlySpakingParticipants,
      ...recentlySpokingParticipants,
      ...otherParticipants
    ];

    // Return up to maxCount participants
    return prioritizedList.slice(0, maxCount);
  }, [speakingHistory]);

  // Periodic update for speaking priority (cleanup old speaking history)
  useEffect(() => {
    const interval = setInterval(() => {
      const now = Date.now();
      setSpeakingHistory(prev => {
        const updated = new Map();
        prev.forEach((lastSpeakingTime, participantId) => {
          // Keep only recent speaking history
          if ((now - lastSpeakingTime) <= SPEAKING_PRIORITY_CONFIG.STICKY_DURATION * 2) {
            updated.set(participantId, lastSpeakingTime);
          }
        });
        return updated;
      });
    }, SPEAKING_PRIORITY_CONFIG.UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [setSpeakingHistory]);

  // Get remote participants with advanced speaking priority
  // When screen sharing: max 2 remotes, when no screen share: max 3 remotes
  const maxRemotes = isScreenShareActive ? 2 : 3;
  const allRemoteParticipants = allParticipants.filter((participant) => !participant.isLocal);
  const remoteParticipants = getAdvancedSpeakingPriorityParticipants(allRemoteParticipants, maxRemotes);



  // Create remote participant data with their camera tracks (if any)
  const remoteParticipantsWithTracks = remoteParticipants.map((participant) => {
    const cameraTrack = allTracks
      .filter(isTrackReference)
      .find((track) =>
        track.participant.identity === participant.identity &&
        track.source === Track.Source.Camera
      );

    return {
      participant,
      track: cameraTrack || null
    };
  });



  // Check if local participant is speaking
  const isSpeaking = localParticipant?.isSpeaking || false;

  // Get participant count for dynamic grid (now based on all remote participants)
  const participantCount = remoteParticipants.length;

  // Dynamic grid class based on participant count, screen share status, and control position
  const getGridClass = (count, controlPos, hasScreenShare) => {
    const baseClass = controlPos === 'top' ? 'control-top' : 'control-bottom';
    const screenSharePrefix = hasScreenShare ? 'pip-grid-screenshare' : 'pip-grid';

    if (hasScreenShare) {
      // WITH SCREEN SHARE layouts
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        default: return `${screenSharePrefix}-three ${baseClass}`;
      }
    } else {
      // NO SCREEN SHARE layouts (your existing perfect logic)
      switch(count) {
        case 0: return `${screenSharePrefix}-solo ${baseClass}`;
        case 1: return `${screenSharePrefix}-two ${baseClass}`;
        case 2: return `${screenSharePrefix}-three ${baseClass}`;
        case 3: return `${screenSharePrefix}-four ${baseClass}`;
        default: return `${screenSharePrefix}-four ${baseClass}`;
      }
    }
  };




  const avatarName = localParticipant?.name
    ? generateAvatar(localParticipant.name)
    : generateAvatar(localParticipant.identity);
  let avatarColor = '#7C4DFF';
  try {
    const metaColor = parseMetadata(localParticipant?.metadata)?.color;
    if (metaColor) avatarColor = metaColor;
  } catch (e) { /* ignore */ }


  const getRemoteParticipantInfo = (participant) => {
    const name = participant?.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
    let color = '#7C4DFF';
    try {
      const metaColor = parseMetadata(participant?.metadata)?.color;
      if (metaColor) color = metaColor;
    } catch (e) { /* ignore */ }
    return { name, color };
  };


  const ControlTile = useMemo(() => {
    return (
      <div className="pip-tile pip-control-tile">
        <div className="pip-control-content">
          <div className="pip-control-buttons">
            <TrackToggle
              source={Track.Source.Microphone}
              showIcon
              className="pip-control-button"
            />
            <TrackToggle
              source={Track.Source.Camera}
              showIcon
              className="pip-control-button"
            />
            {/* Only show screen share button when screen sharing is active */}
            {isScreenShareActive && (
              <TrackToggle
                source={Track.Source.ScreenShare}
                showIcon
                className="pip-control-button"
              />
            )}
            <DisconnectButton
              className="pip-control-button pip-disconnect-button"
            >
              <EndCallIconPip />
            </DisconnectButton>
          </div>
        </div>
      </div>
    );
  }, [isScreenShareActive]);

  return (
    <div className="pip-main-container">
      <div className={`pip-grid-container ${getGridClass(participantCount, controlPosition, isScreenShareActive)}`}>




        {isScreenShareActive ? (
          <>            {/* Screen Share Tile - Always the big tile on top */}
            <div className="pip-tile pip-tile-screenshare">
              {activeScreenShareTrack ? (
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={activeScreenShareTrack}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                  
                  {/* {(isHost || isCoHost) && (
                    <div className="pip-screen-capture-button">
                      <ScreenCaptureButton
                        room={room}
                        screenShareTracks={screenShareTracks}
                        focusTrack={activeScreenShareTrack}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        setShowPopover={() => {}} // No popover in PiP
                      />
                    </div>
                  )} */}
                </div>
              ) : (
                <div className="pip-tile-center-dot" />
              )}
            </div>

            {/* Local Participant Tile - Below screen share */}
            {/* MEMORY OPTIMIZATION: When screen sharing, show only avatar for local participant (no video) */}
            <div className={`pip-tile pip-tile-small pip-tile-local ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
              {/* Always show avatar for local participant when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
              {isSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          </>
        ) : (
          // NO SCREEN SHARE: Local participant as main tile
          <div className={`pip-tile pip-tile-main ${isSpeaking ? 'pip-tile-speaking' : ''}`}>
            {localCameraTrack?.publication && !localCameraTrack.publication.isMuted ? (
              // Show video when camera is on
              <div className="pip-video-container">
                <VideoTrack
                  trackRef={localCameraTrack}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    borderRadius: '2vmin'
                  }}
                />
              </div>
            ) : (
              // Show avatar when camera is off
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: avatarColor }}
                >
                  {avatarName}
                </div>
              </div>
            )}

            {/* Corner accent lights for local speaking */}
            {isSpeaking && (
              <>
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  left: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.6s'
                }} />
                <div style={{
                  position: 'absolute',
                  bottom: '1vmin',
                  right: '1vmin',
                  width: '1.5vmin',
                  height: '1.5vmin',
                  background: '#1e8cfa',
                  borderRadius: '50%',
                  zIndex: 2,
                  animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                  animationDelay: '0.9s'
                }} />
              </>
            )}
          </div>
        )}

        {/* Dynamic remote participant tiles - Only show when NO screen share */}
        {!isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant, track } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          return (
            <div
              key={participant.identity || `remote-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {track?.publication && !track.publication.isMuted ? (
                // Show remote video
                <div className="pip-video-container">
                  <VideoTrack
                    trackRef={track}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'contain',
                      borderRadius: '2vmin'
                    }}
                  />
                </div>
              ) : (
                // Show remote avatar when camera is off
                <div className="pip-tile-avatar-center">
                  <div
                    className="pip-tile-avatar pip-tile-avatar-small"
                    style={{ backgroundColor: remoteInfo.color }}
                  >
                    {remoteInfo.name}
                  </div>
                </div>
              )}

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

        {/* Dynamic remote participant tiles - Only show when WITH screen share */}
        {/* MEMORY OPTIMIZATION: When screen sharing, show only avatars for remote participants */}
        {isScreenShareActive && remoteParticipantsWithTracks.map((participantData, index) => {
          const { participant } = participantData;
          const remoteInfo = getRemoteParticipantInfo(participant);
          const isRemoteSpeaking = participant?.isSpeaking || false;

          return (
            <div
              key={participant.identity || `remote-screenshare-${index}`}
              className={`pip-tile pip-tile-small pip-tile-remote ${isRemoteSpeaking ? 'pip-tile-speaking' : ''}`}
            >
              {/* Always show avatar for remote participants when screen sharing (memory optimization) */}
              <div className="pip-tile-avatar-center">
                <div
                  className="pip-tile-avatar pip-tile-avatar-small"
                  style={{ backgroundColor: remoteInfo.color }}
                >
                  {remoteInfo.name}
                </div>
              </div>

              {/* Corner accent lights for remote speaking */}
              {isRemoteSpeaking && (
                <>
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    left: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.6s'
                  }} />
                  <div style={{
                    position: 'absolute',
                    bottom: '0.5vmin',
                    right: '0.5vmin',
                    width: '1vmin',
                    height: '1vmin',
                    background: '#1e8cfa',
                    borderRadius: '50%',
                    zIndex: 2,
                    animation: 'pip-corner-pulse 1.8s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    animationDelay: '0.9s'
                  }} />
                </>
              )}
            </div>
          );
        })}

          {/* Control tile - positioned at bottom if controlPosition is 'bottom' */}
          {controlPosition === 'bottom' && ControlTile}
        </div>
    </div>
  );
}

export function usePictureInPicture({
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  localParticipant,
  room, // eslint-disable-line no-unused-vars
  controlPosition = 'bottom',
  isHost, // eslint-disable-line no-unused-vars
  isCoHost // eslint-disable-line no-unused-vars
}) {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Automatic PiP context
  const {
    isAutomaticPiPEnabled,
    setIsAutomaticPiPEnabled,
    hasAskedPermission,
    setHasAskedPermission
  } = useAutomaticPiPContext();

  // Get participant count for dynamic sizing
  const allTracks = useTracks([
    { source: Track.Source.Camera, withPlaceholder: true },
  ]);
  const remoteTracks = allTracks
    .filter(isTrackReference)
    .filter((track) => !track.participant.isLocal);
  const participantCount = remoteTracks.length;

  // Dynamic PiP window configuration based on participant count
  const getDynamicConfig = (count) => {
    switch(count) {
      case 0: // Solo - minimum size
        return { width: 180, height: 297 };  // Increased from 270
      case 1: // Two participants - small window
        return { width: 220, height: 363 };  // Increased from 330
      case 2: // Three participants - medium window
        return { width: 280, height: 429 };  // Increased from 390
      case 3: // Four participants - larger window
        return { width: 320, height: 495 };  // Increased from 450
      default:
        return { width: 320, height: 528 };  // Increased from 480
    }
  };

  const defaultConfig = useMemo(() => getDynamicConfig(participantCount), [participantCount]);

  // Track previous participant count for notifications
  const prevParticipantCountRef = useRef(participantCount);

  // Auto-resize PiP window when participant count changes
  useEffect(() => {
    if (pipWindowRef.current && pipWindowDocument) {
      const newConfig = getDynamicConfig(participantCount);
      const prevCount = prevParticipantCountRef.current;

      try {
        // Resize the existing PiP window
        pipWindowRef.current.resizeTo(newConfig.width, newConfig.height);

        // Show notification about participant change (only if PiP is active)
        if (prevCount !== participantCount && prevCount !== undefined) {
          const participantChange = participantCount > prevCount ? 'joined' : 'left';
          const totalParticipants = participantCount + 1; // +1 for local participant

          setToastNotification(`Participant ${participantChange}. PiP adjusted for ${totalParticipants} participant${totalParticipants > 1 ? 's' : ''}.`);
          setToastStatus("info");
          setShowToast(true);
        }

      } catch (error) {
        console.log('PiP resize not supported on this browser');
      }
    }

    // Update previous count
    prevParticipantCountRef.current = participantCount;
  }, [participantCount, pipWindowDocument, setToastNotification, setToastStatus, setShowToast]);

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Check if automatic PiP is supported
  const isAutomaticPiPSupported = useMemo(() => {
    const hasMediaSession = 'mediaSession' in navigator && 'setActionHandler' in navigator.mediaSession;

    // Check Chrome version (automatic PiP requires Chrome 120+)
    const isChrome = /Chrome/.test(navigator.userAgent);
    const chromeVersion = isChrome ? parseInt(navigator.userAgent.match(/Chrome\/(\d+)/)?.[1] || '0') : 0;

    console.log('🔍 Browser compatibility check:', {
      hasMediaSession,
      isChrome,
      chromeVersion,
      userAgent: navigator.userAgent,
      supported: hasMediaSession && isChrome && chromeVersion >= 120
    });

    return hasMediaSession && isChrome && chromeVersion >= 120;
  }, []);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Forward declaration for openPipWindow
  const openPipWindowRef = useRef(null);

  // Store the action handler for manual testing
  const automaticPiPHandlerRef = useRef(null);

  // Ask for automatic PiP permission
  const askForAutomaticPiPPermission = useCallback(async () => {
    console.log('🎯 askForAutomaticPiPPermission called');
    console.log('🎯 isAutomaticPiPSupported:', isAutomaticPiPSupported);

    if (!isAutomaticPiPSupported) {
      console.log('❌ Automatic PiP not supported');
      setToastNotification("Automatic PiP not supported in this browser");
      setToastStatus("error");
      setShowToast(true);
      return false;
    }

    try {
      console.log('🎯 Registering media session action handler...');

      // Create the handler function
      const handler = async () => {
        console.log('🚀🚀🚀 AUTOMATIC PIP TRIGGERED BY TAB SWITCH! 🚀🚀🚀');
        console.log('📱 Media session action handler called');
        console.log('🔍 openPipWindowRef.current exists:', !!openPipWindowRef.current);

        // This will be called when user switches tabs
        if (openPipWindowRef.current) {
          console.log('📞 Calling openPipWindow from automatic trigger...');
          try {
            const success = await openPipWindowRef.current();
            console.log('✅ Automatic PiP window opened successfully:', success);
            if (success) {
              setToastNotification("Automatic Picture-in-Picture activated");
              setToastStatus("success");
              setShowToast(true);
            } else {
              console.log('❌ openPipWindow returned false');
            }
          } catch (error) {
            console.log('❌ Error in automatic PiP trigger:', error);
          }
        } else {
          console.log('❌ openPipWindowRef.current is null - cannot open PiP');
        }
      };

      // Store handler for manual testing and register it
      automaticPiPHandlerRef.current = handler;
      navigator.mediaSession.setActionHandler('enterpictureinpicture', handler);

      // Set media session metadata to enhance user experience
      if ('mediaSession' in navigator) {
        console.log('🎯 Setting media session metadata...');
        navigator.mediaSession.metadata = new MediaMetadata({
          title: 'Video Conference',
          artist: 'Daakia',
          album: 'Live Meeting',
          artwork: [
            { src: '/favicon.ico', sizes: '96x96', type: 'image/x-icon' }
          ]
        });

        // Set playback state to "playing" - this might be required for automatic PiP
        console.log('🎯 Setting media session playback state to "playing"...');
        navigator.mediaSession.playbackState = 'playing';

        // Add other media session handlers that might be required
        console.log('🎯 Setting up additional media session handlers...');
        navigator.mediaSession.setActionHandler('play', () => {
          console.log('📱 Media session play action called');
        });
        navigator.mediaSession.setActionHandler('pause', () => {
          console.log('📱 Media session pause action called');
        });

        // Check media session state
        console.log('🔍 Media session state after setup:', navigator.mediaSession.playbackState);
        console.log('🔍 Media session metadata:', navigator.mediaSession.metadata);

        // Check what media elements Chrome can see on the page (after hidden element is created)
        setTimeout(() => {
          const videoElements = document.querySelectorAll('video');
          const audioElements = document.querySelectorAll('audio');
          console.log('🔍 Media elements on page (after hidden element):', {
            videoCount: videoElements.length,
            audioCount: audioElements.length,
            videos: Array.from(videoElements).map(v => ({
              src: v.src || 'stream',
              paused: v.paused,
              muted: v.muted,
              readyState: v.readyState,
              playing: !v.paused && !v.ended && v.readyState > 2
            })),
            audios: Array.from(audioElements).map(a => ({
              src: a.src || 'stream',
              paused: a.paused,
              muted: a.muted,
              readyState: a.readyState,
              playing: !a.paused && !a.ended && a.readyState > 2,
              srcObject: !!a.srcObject
            }))
          });
        }, 2000);
      }

      setIsAutomaticPiPEnabled(true);
      setHasAskedPermission(true);

      console.log('✅ Automatic PiP enabled successfully');
      console.log('🔍 Final check - room state:', room?.state);

      // Check track publications properly (the LiveKit way)
      const micPublication = room?.localParticipant?.getTrackPublication(Track.Source.Microphone);
      const cameraPublication = room?.localParticipant?.getTrackPublication(Track.Source.Camera);

      const hasMic = micPublication?.track && !micPublication?.isMuted;
      const hasCamera = cameraPublication?.track && !cameraPublication?.isMuted;

      console.log('🔍 Track publications check:', {
        micPublication: !!micPublication,
        micTrack: !!micPublication?.track,
        micMuted: micPublication?.isMuted,
        micEnabled: micPublication?.isEnabled,
        hasMic,
        cameraPublication: !!cameraPublication,
        cameraTrack: !!cameraPublication?.track,
        cameraMuted: cameraPublication?.isMuted,
        cameraEnabled: cameraPublication?.isEnabled,
        hasCamera
      });

      // Check if user has active media (required for automatic PiP)
      if (!hasMic && !hasCamera) {
        console.log('⚠️ WARNING: No active camera/microphone detected!');
        setToastNotification("Automatic PiP enabled. Note: Turn ON camera/microphone for it to work when switching tabs.");
        setToastStatus("warning");
      } else {
        console.log('✅ Active media detected - automatic PiP should work!');
        setToastNotification("Automatic PiP enabled. Switch tabs to activate.");
        setToastStatus("info");
      }
      setShowToast(true);

      return true;
    } catch (error) {
      console.error('❌ Failed to register automatic PiP:', error);
      setToastNotification("Failed to enable automatic Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
      return false;
    }
  }, [isAutomaticPiPSupported, setToastNotification, setToastStatus, setShowToast, setIsAutomaticPiPEnabled, setHasAskedPermission]);

  // Toggle automatic PiP
  const toggleAutomaticPiP = useCallback(async (enabled) => {
    console.log('🎯 toggleAutomaticPiP called with enabled:', enabled);

    if (enabled) {
      console.log('🎯 Enabling automatic PiP...');
      return askForAutomaticPiPPermission();
    } else {
      console.log('🎯 Disabling automatic PiP...');
      try {
        // Remove the action handler to disable automatic PiP
        navigator.mediaSession.setActionHandler('enterpictureinpicture', null);
        setIsAutomaticPiPEnabled(false);

        console.log('✅ Automatic PiP disabled successfully');
        setToastNotification("Automatic Picture-in-Picture disabled");
        setToastStatus("info");
        setShowToast(true);

        return true;
      } catch (error) {
        console.error('❌ Failed to disable automatic PiP:', error);
        return false;
      }
    }
  }, [askForAutomaticPiPPermission, setIsAutomaticPiPEnabled, setToastNotification, setToastStatus, setShowToast]);

  // Create a hidden media element for Chrome to detect
  const hiddenMediaRef = useRef(null);

  // Setup hidden media element for automatic PiP detection
  useEffect(() => {
    if (isAutomaticPiPEnabled && room?.localParticipant) {
      console.log('🎯 Setting up hidden media element for Chrome detection...');

      // Create a hidden audio element
      const audio = document.createElement('audio');
      audio.style.display = 'none';
      audio.muted = true; // Start muted to avoid audio feedback
      audio.autoplay = true;
      audio.loop = true;

      // Try to get the actual media stream from LiveKit
      const micPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
      if (micPublication?.track) {
        console.log('🎯 Connecting LiveKit audio track to hidden element...');
        const mediaStream = new MediaStream([micPublication.track.mediaStreamTrack]);
        audio.srcObject = mediaStream;

        // Add to DOM first
        document.body.appendChild(audio);
        hiddenMediaRef.current = audio;

        // Try to play the audio element
        audio.play().then(() => {
          console.log('✅ Hidden audio element is playing');
          console.log('🔍 Hidden audio state:', {
            paused: audio.paused,
            muted: audio.muted,
            readyState: audio.readyState,
            currentTime: audio.currentTime,
            duration: audio.duration,
            srcObject: !!audio.srcObject
          });
        }).catch(error => {
          console.log('⚠️ Hidden audio play failed:', error);
          // Try unmuting and playing again
          audio.muted = false;
          audio.play().catch(e => console.log('⚠️ Second play attempt failed:', e));
        });

        console.log('✅ Hidden media element created and connected to LiveKit stream');
      } else {
        console.log('⚠️ No microphone track found for hidden media element');
      }
    }

    return () => {
      if (hiddenMediaRef.current) {
        console.log('🧹 Cleaning up hidden media element...');
        document.body.removeChild(hiddenMediaRef.current);
        hiddenMediaRef.current = null;
      }
    };
  }, [isAutomaticPiPEnabled, room?.localParticipant]);

  // Check Chrome's automatic PiP permissions
  const checkAutomaticPiPPermissions = useCallback(async () => {
    console.log('🔍 Checking Chrome automatic PiP permissions...');

    try {
      // Check if we can query permissions
      if ('permissions' in navigator) {
        // Note: 'automatic-picture-in-picture' might not be queryable yet
        const result = await navigator.permissions.query({name: 'camera'});
        console.log('🔍 Camera permission:', result.state);

        const micResult = await navigator.permissions.query({name: 'microphone'});
        console.log('🔍 Microphone permission:', micResult.state);
      }

      // Check if there are any playing media elements
      const playingMedia = Array.from(document.querySelectorAll('video, audio')).filter(el =>
        !el.paused && !el.ended && el.readyState > 2
      );
      console.log('🔍 Currently playing media elements:', playingMedia.length);

      // Check user engagement
      console.log('🔍 User activation:', {
        hasBeenActive: navigator.userActivation?.hasBeenActive,
        isActive: navigator.userActivation?.isActive
      });

    } catch (error) {
      console.log('⚠️ Permission check failed:', error);
    }
  }, []);

  // Manual test function to trigger automatic PiP handler directly
  const testAutomaticPiPHandler = useCallback(async () => {
    console.log('🧪 MANUAL TEST: Triggering automatic PiP handler directly...');

    // First check permissions and conditions
    await checkAutomaticPiPPermissions();

    if (automaticPiPHandlerRef.current) {
      try {
        await automaticPiPHandlerRef.current();
        console.log('✅ Manual test completed');
      } catch (error) {
        console.log('❌ Manual test failed:', error);
      }
    } else {
      console.log('❌ No automatic PiP handler found');
    }
  }, [checkAutomaticPiPPermissions]);

  // Simple PiP Content
  const PipContent = useCallback(() => {
    return <SimplePipContent
      localParticipant={localParticipant}
      controlPosition={controlPosition}
    />;
  }, [localParticipant, controlPosition]);




  // Simple error handling
  const handlePipError = useCallback(() => {
    setToastNotification("Failed to open Picture-in-Picture");
    setToastStatus("error");
    setShowToast(true);
  }, [setToastNotification, setToastStatus, setShowToast]);

  // Simple PiP window opening
  const openPipWindow = useCallback(async () => {
    console.log('🎯 openPipWindow called');
    console.log('🔍 isSupported:', isSupported);
    console.log('🔍 pipWindowRef.current:', pipWindowRef.current);

    if (!isSupported) {
      console.log('❌ Document Picture-in-Picture not supported');
      handlePipError(new Error('Document Picture-in-Picture not supported'));
      return false;
    }

    if (pipWindowRef.current) {
      console.log('✅ PiP window already exists, returning true');
      return true;
    }

    try {
      console.log('🚀 Requesting PiP window with config:', defaultConfig);
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      console.log('✅ PiP window created successfully:', pipWindow);
      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document and copy styles from main document
      const pipDoc = pipWindow.document;

      // Copy all stylesheets from the main document to PiP window
      const mainStyleSheets = Array.from(document.styleSheets);
      mainStyleSheets.forEach((styleSheet) => {
        try {
          if (styleSheet.href) {
            // External stylesheet
            const link = pipDoc.createElement('link');
            link.rel = 'stylesheet';
            link.href = styleSheet.href;
            pipDoc.head.appendChild(link);
          } else if (styleSheet.ownerNode) {
            // Inline stylesheet
            const clonedStyle = styleSheet.ownerNode.cloneNode(true);
            pipDoc.head.appendChild(clonedStyle);
          }
        } catch (e) {
          // Handle CORS issues with external stylesheets
          console.warn('Could not copy stylesheet:', e);
        }
      });

      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Simple close handler
      pipWindow.addEventListener('pagehide', () => {
        console.log('🔄 PiP window pagehide event triggered');
        closePipWindow();
      });

      console.log('✅ PiP window setup complete');
      return true;
    } catch (error) {
      console.log('❌ Error creating PiP window:', error);
      handlePipError(error);
      return false;
    }
  }, [isSupported, defaultConfig, setIsPIPEnabled, closePipWindow, handlePipError]);

  // Update the ref when openPipWindow changes
  useEffect(() => {
    openPipWindowRef.current = openPipWindow;
  }, [openPipWindow]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    console.log('🎯 togglePipMode called with enabled:', enabled);
    if (enabled) {
      console.log('🚀 Opening PiP window...');
      return openPipWindow();
    } else {
      console.log('🔄 Closing PiP window...');
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Add visibility change listener to debug tab switching
  useEffect(() => {
    const handleVisibilityChange = () => {
      console.log('👁️ Page visibility changed:', document.hidden ? 'HIDDEN' : 'VISIBLE');
      console.log('🔍 Automatic PiP enabled:', isAutomaticPiPEnabled);
      console.log('🔍 Media session playback state:', navigator.mediaSession?.playbackState);
      console.log('🔍 Document has user activation:', navigator.userActivation?.hasBeenActive);

      if (document.hidden && isAutomaticPiPEnabled) {
        console.log('🚀 Tab switched away - automatic PiP should trigger now!');
        console.log('🔍 Checking all conditions for automatic PiP...');

        // Check if we have the handler registered
        console.log('🔍 Handler registered:', !!automaticPiPHandlerRef.current);

        // Try to manually check if Chrome would trigger automatic PiP
        setTimeout(() => {
          console.log('⏰ 1 second after tab switch - checking if automatic PiP triggered...');
          if (!pipWindowRef.current) {
            console.log('❌ Automatic PiP did NOT trigger - Chrome is not calling our handler');
            console.log('💡 This suggests Chrome doesn\'t think conditions are met for automatic PiP');
          }
        }, 1000);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAutomaticPiPEnabled]);

  // Clean up automatic PiP on unmount
  useEffect(() => {
    return () => {
      if (isAutomaticPiPEnabled) {
        try {
          navigator.mediaSession.setActionHandler('enterpictureinpicture', null);
        } catch (error) {
          console.warn('Failed to cleanup automatic PiP:', error);
        }
      }
    };
  }, [isAutomaticPiPEnabled]);

  // Simple PiP content rendering
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <div className="pip-container">
        <PipContent />
      </div>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, PipContent]);

  return {
    togglePipMode,
    askForAutomaticPiPPermission,
    toggleAutomaticPiP,
    testAutomaticPiPHandler,
    pipPortal,
    isSupported,
    isAutomaticPiPSupported,
    isAutomaticPiPEnabled,
    hasAskedPermission,
    controlPosition
  };
}
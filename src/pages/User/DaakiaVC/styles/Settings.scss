.record-popover {
  padding: 0;
  .ant-popover-inner-content {
    width: 100%;
  }
}
.setting-control-button-popover {
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  .ant-popover-inner-content {
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .ant-popover-inner {
    border-radius: 10px;
  }
}
.recording-button{
  background-color: transparent;
  border: none;
  &:hover{
    background-color: transparent;
    border: none;
  }
}
.settings-menu-item {
  color: white;
  padding: 2% 4%;
  display: flex;
  justify-content: start;
  cursor: pointer;
  align-items: center;
  // height: 3em;
  margin: 5px;
  gap: 0.7em;
  width: 100%;
  &:hover {
    background-color: #eff1f4;
    color: #000;
    border-radius: 6px;
    .settings-menu-inner-icon {
      svg {
        color: #000;
        filter: saturate(0%) hue-rotate(200deg) brightness(50%) contrast(90%);
      }
    }
  }
  .settings-menu-inner-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 20px;
    svg {
      color: black;
      width: 23px;
      height: 23px;
    }
  }

  // Settings Control Icons
  .ve-icon {
    svg {
      width: 19px !important;
      height: 19px !important;
    }
  }
  .live-cap-icon {
    svg {
      width: 20px !important;
      height: 20px !important;
    }
  }
  .troubleshoot-icon{
    svg{
      width: 18px !important;
      height: 18px !important;
    }
  }
  .live-stream-icon{
    svg{
      width: 19px !important;
      height: 19px !important;
    }
  }
  .report-icon{
    svg{
      width: 19px !important;
      height: 19px !important;
    }
  }
  .full-screen-icon{
    svg{
      width: 18px !important;
      height: 18px !important;
    }
  }

  .settings-menu-inner-text {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 1rem;
    width: auto;
  }
}
.setting-control-button {
  width: 11rem;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* Media Queries for Responsiveness */
@media (max-width: 875px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.6em;
    height: 2.8em;
  }

  .settings-menu-inner-text {
    font-size: 0.8rem;
  }

  .settings-menu-inner-icon svg {
    width: 20px;
    height: 20px;
  }
}

@media (max-width: 710px) {
  .settings-menu-item {
    padding: 2% 3%;
    gap: 0.5em;
    height: 2.5em;
  }

  .settings-menu-inner-text {
    font-size: 0.7rem;
  }

  .settings-menu-inner-icon svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 450px) {
  .setting-control-button-popover {
    z-index: 11;
  }
  .setting-control-button {
    width: auto;
    .mvt-options {
      margin: 0;
      padding: 5px 10px;
    }
  }
}

.setting-control-button-mobile {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  gap: 0.5rem;
}

// Combined PiP button styles
.pip-text-flex {
  flex: 1;
}

.auto-pip-button {
  margin-left: 8px;
  cursor: pointer;

  .auto-pip-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: #fff;

    &.enabled {
      background-color: #1e8cfa;
    }

    &.disabled {
      background-color: #bfbfbf;
    }
  }
}

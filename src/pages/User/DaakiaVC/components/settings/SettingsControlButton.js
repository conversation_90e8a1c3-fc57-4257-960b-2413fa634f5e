import { <PERSON><PERSON>, <PERSON><PERSON>, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import { isMobileBrowser } from "@livekit/components-core";
import isElectron from "is-electron";

import { LivestreamingUrlModal } from "./LivestreamModal";

import { SettingsMenuServices } from "../../services/SettingsMenuServices";
import { WhiteboardService } from "../../services/WhiteboardServices";

import "../../styles/ControlBar.scss";
import "../../styles/Settings.scss";
import "../../styles/index.scss";

import { DataReceivedEvent, DrawerState } from "../../utils/constants";

import { ReactComponent as VisualEffectIcon } from "./icons/VirtualEffects.svg";
// import { ReactComponent as LeaveIcon } from "../../assets/icons/LeaveIcon.svg";
import { ReactComponent as LiveStreamStart } from "./icons/LiveStreamingOff.svg";
import { ReactComponent as LiveStreamStop } from "./icons/LiveStreamingOn.svg";
import { ReactComponent as ThreeDotsIcon } from "./icons/ThreeDots.svg";
import { ReactComponent as ThreeDotsIconBlue } from "./icons/ThreeDotsBlue.svg";
import { ReactComponent as ReportIcon } from "./icons/ReportProblem.svg";
import { ReactComponent as TroubleShootIcon } from "./icons/Troubleshoot.svg";
import { ReactComponent as StopRecordingIcon } from "./icons/StopRecording.svg";
import { ReactComponent as MaximizeIcon } from "../../assets/icons/Maximize.svg";
import { ReactComponent as MinimizeIcon } from "../../assets/icons/Minimize.svg";
// import recordingStop from "../../assets/sounds/recording_stop.mp3";
import { ReactComponent as SvgParticipantIcon } from "../participants/icons/ParticipantIcon.svg";
import { ReactComponent as SvgParticipantBlueIcon } from "../participants/icons/ParticpantOnIcon.svg";

// Context import
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { ReactComponent as SvgRaiseHandIcon } from "../raisehand/icons/RaiseHandIcon.svg";
import { ReactComponent as SvgRaiseHandOnIcon } from "../raisehand/icons/RaiseHandON.svg";
import { ReactComponent as SvgHostControlIcon } from "./icons/HostControl.svg";
import { ReactComponent as RecordingIcon } from "../../assets/icons/Recording.svg";
import { ReactComponent as LiveCaptionsIcon } from "./icons/LiveCaptions.svg";
import { ReactComponent as SettingsIcon } from "./icons/SettingsIco.svg";
import { ReactComponent as PictureInPicture } from "./icons/pip.svg";
import { ReactComponent as Whiteboard } from "./icons/Whiteboard.svg";
import { TroubleShootAndHelp } from "./TroubleShootAndHelp";
import SettingsModal from "./SettingsModal";
import { generateAvatar, parseMetadata } from "../../utils/helper";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";
import { SaasService } from "../../SaaS/services/saasServices";

export function SettingsControlButton({
  id,
  isHost,
  isCoHost,
  meetingDetails,
  coHostToken,
  showRaiseHand,
  setShowRaiseHand,
  showRecording,
  isBreakoutRoom,
  room,
  meetingFeatures,
  showlivecaptionsicon,
  isPIPEnabled,
  setIsPIPEnabled,
  isAutomaticPiPSupported,
  isAutomaticPiPEnabled,
  hasAskedPermission, // eslint-disable-line no-unused-vars
  askForAutomaticPiPPermission, // eslint-disable-line no-unused-vars
  toggleAutomaticPiP,
  isWhiteboardOpen,
  setIsWhiteboardOpen,
  // isExitWhiteboardModalOpen,
  whiteboardSceneData,
  setIsExitWhiteboardModalOpen,
  setWhiteboardSceneData,
  whiteBoardId,
  setWhiteboardId,
  // setShowRecording,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  setToastNotification,
  setToastStatus,
  setShowToast,
  brightness,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff,
  onAutoAudioOffChange,
  isRecordingLoading,
  setIsRecordingLoading,
  setParticipantConsent,
  remoteParticipants,
  speakerDeviceId,
  setSpeakerDeviceId,
  deviceIdAudio,
  setDeviceIdAudio,
}) {
  // Context
  const { openDrawer, setOpenDrawer } = useVideoConferencesContext();
  const { saasHostToken } = useSaasHelpers();
  // const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [showPopover, setShowPopover] = useState(false);
  const [isRecording, setIsRecording] = useState(
    // meetingDetails?.is_recording_active === true || showRecording === true
    showRecording || meetingDetails?.is_recording_active
  );
  const [egressId, setEgressId] = useState(
    meetingDetails?.meeting_recordings[0]?.egress_id || null
  );
  const [isLiveStreamModalOpen, setIsLiveStreamModalOpen] = useState(false);
  const [isLiveStreaming, setIsLiveStreaming] = useState(false);
  const [isTroubleshooting, setIsTroubleshooting] = useState(false);
  // const [isCloudRecording, setIsCloudRecording] = useState(true);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  const remoteParticipantsArray = Array.from(remoteParticipants.values());

  useEffect(() => {
    setIsRecording(showRecording);
  }, [isRecording, showRecording, meetingDetails?.is_recording_active]);

  const [initialRecordDisabled, setInitialRecordDisabled] = React.useState(false);

  React.useEffect(() => {
    if (meetingDetails?.meeting_config?.recording_force_stopped === 1) {
      setInitialRecordDisabled(false); // Use normal logic
    } else if (meetingDetails?.meeting_config?.auto_start_recording === 1) {
      setInitialRecordDisabled(true); // Disable Record button
    }
    // Only run on mount
    // eslint-disable-next-line
  }, []);

  const openWhiteboard = async () => {
    let whiteboardId = null;
    try {
      if (
        meetingDetails?.meeting_whiteboards.length === 0 &&
        (whiteBoardId === null || whiteBoardId === undefined)
      ) {
        const response = await WhiteboardService.saveWhiteboard(
          id,
          [],
          room?.localParticipant?.participantInfo,
          saasHostToken || coHostToken
        );
        if (response.success === 0) {
          console.log("Error in opening whiteboard", response.message);
        }
        whiteboardId = response.data.whiteboardId;
      } else {
        whiteboardId = whiteBoardId;
      }

      const updateResponse = await WhiteboardService.updateWhiteboardStatus(
        whiteboardId,
        !isWhiteboardOpen,
        id,
        room?.localParticipant?.participantInfo,
        saasHostToken || coHostToken
      );
      if (updateResponse.success === 0) {
        console.log(
          "Error in Updating status of whiteboard",
          updateResponse.message
        );
      }
    } catch (error) {
      setShowToast(true);
      setToastNotification(error.message);
      setToastStatus("error");
      // console.log("Error in opening whiteboard", error);
    }
    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.WHITEBOARD_STATE,
        value: !isWhiteboardOpen,
        whiteboardId,
      })
    );
    await room.localParticipant.publishData(data, {
      reliable: true,
    });
    setWhiteboardId(whiteboardId);
    if (isWhiteboardOpen) {
      if (whiteboardSceneData.start !== whiteboardSceneData.end) {
        setIsExitWhiteboardModalOpen(true);
      } else {
        setWhiteboardSceneData((prevData) => ({
          ...prevData,
          start: prevData.end,
        }));
        setIsWhiteboardOpen(false);
      }
    } else setIsWhiteboardOpen(true);
  };

  const stopCloudRecording = async () => {
    setIsRecordingLoading(true);
    try {
      const response = await SettingsMenuServices.stopCloudRecording(
        id,
        egressId,
        room?.localParticipant?.participantInfo,
        saasHostToken || coHostToken
      );
      if (response.success === 0) {
        setToastNotification("Error in stopping recording");
        setToastStatus("error");
        setShowToast(true);
        console.log("Error in stopping recording", response.message);
        return;
      }
      setIsRecording(false);
      setEgressId(null);
      // setIsCloudRecording(false);
    } catch (error) {
      setShowToast(true);
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error in recording", error);
    } finally {
      setShowPopover(false);
    }
  };

  // const stopLocalRecording = () => {
  //   console.log("Local recording stopped");
  //   setIsRecording(false);
  //   setIsCloudRecording(false);
  //   setShowPopover(false);
  // };

  // const handleStopRecording = async () => {
  //   if (isCloudRecording) {
  //     await stopCloudRecording(id);
  //   } else {
  //     stopLocalRecording();
  //   }
  // };

  const startCloudRecording = async () => {
    setIsRecordingLoading(true);
    setShowPopover(false);
    try {
      if (
        (meetingFeatures?.configurations &&
          meetingFeatures?.configurations?.ask_recording_request === "all")
      ) {
        const encoder = new TextEncoder();
        const data = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.RECORDING_CONSENT_MODAL,
            value: true,
          })
        );
        await room.localParticipant.publishData(data, {
          reliable: true,
        });
        setOpenDrawer((prev) =>
          prev === DrawerState.RECORDING_CONSENT
            ? DrawerState.NONE
            : DrawerState.RECORDING_CONSENT
        );
        const consentData = remoteParticipantsArray.map(participant => ({
          participantId: participant?.identity,
          participantName: participant?.name,
          participantAvatar: generateAvatar(participant?.name),
          consent: "pending",
        }));
        consentData.push({
          participantId: room?.localParticipant?.identity,
          participantName: room?.localParticipant?.name,
          participantAvatar: generateAvatar(room?.localParticipant?.name),
          consent: "accept",
        });
        setParticipantConsent(consentData);
        setIsRecordingLoading(false);

        const metadata = parseMetadata(room?.localParticipant?.metadata);
        const meetingSessionResponse = await SaasService.getMeetingSession(id, saasHostToken);
        if(meetingSessionResponse.success === 0){
          setToastNotification(meetingSessionResponse.message);
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        const recordingConsentResponse = await SaasService.startRecordingConsent(
          id,
          metadata?.current_session_uid?.toString() || meetingSessionResponse?.data?.id.toString(),
          metadata?.meeting_attendance_id.toString(),
          room?.localParticipant?.participantInfo,
          true,
          saasHostToken
        );
        if (recordingConsentResponse.success === 0) {
          setToastNotification(recordingConsentResponse.message);
          setToastStatus("error");
          setShowToast(true);
          return;
        }
        const recrodingData = encoder.encode(
          JSON.stringify({
            action: DataReceivedEvent.STARTED_RECORDING_CONSENT,
            participants: consentData || [],
          })
        );
        room.localParticipant.publishData(recrodingData, {
          reliable: true,
        });
        return;
      }
      const response = await SettingsMenuServices.startCloudRecording(
        id,
        room?.localParticipant?.participantInfo,
        saasHostToken || coHostToken
      );

      if (response.success === 0) {
        setToastNotification("Error in starting recording");
        setToastStatus("error");
        setShowToast(true);
        console.log("Error in starting recording", response.message);
        return;
      }
      setEgressId(response.data.egress_id);
      setIsRecording(true);
      // setIsCloudRecording(true);
    } catch (error) {
      setShowToast(true);
      setToastNotification(error.message);
      setToastStatus("error");
      setShowToast(true);
      // console.log("Error in recording", error);
    } finally {
      setIsRecordingLoading(false);
    }
  };

  function toggleFullScreen() {
    if (!document.fullscreenElement) {
      // Enter fullscreen mode
      document.documentElement.requestFullscreen().catch((err) => {
        // console.error(
        //   `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
        // );
        setToastNotification(
          `Error attempting to enable full-screen mode: ${err.message} (${err.name})`
        );
        setToastStatus("error");
        setShowToast(true);
        setToastStatus("error");
      });
    } else {
      // Exit fullscreen mode
      document.exitFullscreen().catch((err) => {
        // console.error(
        //   `Error attempting to exit full-screen mode: ${err.message} (${err.name})`
        // );
        setToastNotification(
          `Error attempting to exit full-screen mode: ${err.message} (${err.name})`
        );
        setToastStatus("error");
        setShowToast(true);
        setToastStatus("error");
      });
    }
  }

  const content = (
    <div className="primary-font setting-control-button">
      {/* Recording */}
      {!isMobileBrowser() ? (
        <>
          {meetingFeatures?.record_meeting === 1 &&
            (isHost || isCoHost) &&
            !isBreakoutRoom &&
            (isRecording ? (
              <Button
                className="settings-menu-item recording-button"
                onClick={() => {
                  stopCloudRecording();
                  if(initialRecordDisabled) {
                    setInitialRecordDisabled(false);
                  }
                }}
                disabled={isRecordingLoading}
              >
                <div className="settings-menu-inner-icon">
                  <StopRecordingIcon />
                </div>
                <div className="settings-menu-inner-text">Stop Recording</div>
              </Button>
            ) : (
              <Button
                className="settings-menu-item recording-button"
                onClick={() => startCloudRecording()}
                disabled={
                  initialRecordDisabled
                    ? true
                    : (isRecordingLoading || openDrawer === DrawerState.RECORDING_CONSENT)
                }
              >
                <div className="settings-menu-inner-icon">
                  <RecordingIcon />
                </div>
                <div className="settings-menu-inner-text">Record</div>
              </Button>
            ))}
        </>
      ) : null}
      {/* Host Control */}
      {!isMobileBrowser() && meetingFeatures?.webinar_enabled === 1 ? (
        <>
          {(isHost || isCoHost) && (
            <div
              onClick={() => {
                setOpenDrawer((prev) =>
                  prev === DrawerState.HOSTCONTROL
                    ? DrawerState.NONE
                    : DrawerState.HOSTCONTROL
                );
                setShowPopover(false);
              }}
              className="settings-menu-item mvt-options"
            >
              <div className="settings-menu-inner-icon">
                <SvgHostControlIcon />
              </div>
              <div className="settings-menu-inner-text">Host Control</div>
            </div>
          )}
        </>
      ) : null}
      {/* Visual Effects */}
      {!isMobileBrowser() ? (
        <>
          <div
            onClick={() => {
              // setIsVBDrawerOpen(!isVBDrawerOpen);
              setOpenDrawer((prev) =>
                prev === DrawerState.VIRTUAL_BACKGROUND
                  ? DrawerState.NONE
                  : DrawerState.VIRTUAL_BACKGROUND
              );
              setShowPopover(false);
            }}
            className="settings-menu-item"
          >
            <div
              className="settings-menu-inner-icon ve-icon"
              onClick={() => {
                setShowPopover(false);
              }}
            >
              <VisualEffectIcon />
            </div>
            <div className="settings-menu-inner-text">Visual Effects</div>
          </div>
        </>
      ) : null}

      {/* Live Captions */}
      {!isMobileBrowser() && meetingFeatures?.voice_transcription === 1 && (
        <>
          {isHost || isCoHost || showlivecaptionsicon ? (
            <div
              onClick={() => {
                setOpenDrawer((prev) =>
                  prev === DrawerState.LIVECAPTION
                    ? DrawerState.NONE
                    : DrawerState.LIVECAPTION
                );
                setShowPopover(false);
              }}
              className="settings-menu-item"
            >
              <div
                className="settings-menu-inner-icon live-cap-icon"
                onClick={() => {
                  setShowPopover(false);
                }}
              >
                <LiveCaptionsIcon />
              </div>
              <div className="settings-menu-inner-text">Live Captions</div>
            </div>
          ) : null}
        </>
      )}

      {!isMobileBrowser() && !isElectron() ? (
        <>
          <div
            className="settings-menu-item"
            onClick={() => {
              setIsPIPEnabled(!isPIPEnabled);
              setShowPopover(false);
            }}
          >
            <div className="settings-menu-inner-icon troubleshoot-icon">
              <PictureInPicture
                style={{ backgroundColor: "#bfbfbf", borderRadius: "2px" }}
              />
            </div>
            <div className="settings-menu-inner-text">
              <Tooltip title="Picture in Picture" placement="right">
                Toggle PiP
              </Tooltip>
            </div>
          </div>

          {/* Automatic PiP Toggle */}
          {isAutomaticPiPSupported && (
            <div
              className="settings-menu-item"
              onClick={async () => {
                const success = await toggleAutomaticPiP(!isAutomaticPiPEnabled);
                if (success) {
                  setShowPopover(false);
                }
              }}
            >
              <div className="settings-menu-inner-icon troubleshoot-icon">
                <PictureInPicture
                  style={{
                    backgroundColor: isAutomaticPiPEnabled ? "#1e8cfa" : "#bfbfbf",
                    borderRadius: "2px"
                  }}
                />
              </div>
              <div className="settings-menu-inner-text">
                <Tooltip title="Automatic Picture in Picture - Activates when switching tabs" placement="right">
                  Auto PiP {isAutomaticPiPEnabled ? "ON" : "OFF"}
                </Tooltip>
              </div>
            </div>
          )}
        </>
      ) : null}

      {/* TroubleShootAndHelp */}
      {!isMobileBrowser() ? (
        <>
          <div
            className="settings-menu-item"
            onClick={() => {
              setIsTroubleshooting(!isTroubleshooting);
              setShowPopover(false);
            }}
          >
            <div className="settings-menu-inner-icon troubleshoot-icon">
              <TroubleShootIcon />
            </div>
            <div className="settings-menu-inner-text">Troubleshoot</div>
          </div>
          <TroubleShootAndHelp
            isTroubleshooting={isTroubleshooting}
            setIsTroubleshooting={setIsTroubleshooting}
            room={room}
            id={id}
          />
        </>
      ) : null}
      {/* Livestreaming */}
      {!isMobileBrowser() && meetingFeatures?.live_stream === 1 && (
        <>
          {(isHost || isCoHost) && !isBreakoutRoom && (
            <>
              <div
                className="settings-menu-item"
                onClick={() => {
                  setIsLiveStreamModalOpen(!isLiveStreamModalOpen);
                  setShowPopover(false);
                }}
              >
                <div className="settings-menu-inner-icon live-stream-icon">
                  {isLiveStreaming ? <LiveStreamStop /> : <LiveStreamStart />}
                </div>
                <div className="settings-menu-inner-text">
                  {isLiveStreaming ? "Stop Live Streaming" : "Live Streaming"}
                </div>
              </div>
              <LivestreamingUrlModal
                isLiveStreamModalOpen={isLiveStreamModalOpen}
                setIsLiveStreamModalOpen={setIsLiveStreamModalOpen}
                isLiveStreaming={isLiveStreaming}
                setIsLiveStreaming={setIsLiveStreaming}
                id={id}
                localParticipant={room?.localParticipant}
                setToastNotification={setToastNotification}
                setToastStatus={setToastStatus}
                setShowToast={setShowToast}
              />
            </>
          )}
        </>
      )}
      {/* Report an Issue Icon */}
      {!isMobileBrowser() ? (
        <>
          <div
            className="settings-menu-item"
            onClick={() => {
              // setIsRPDrawerOpen(!isRPDrawerOpen);
              setOpenDrawer((prev) =>
                prev === DrawerState.REPORT_ISSUE
                  ? DrawerState.NONE
                  : DrawerState.REPORT_ISSUE
              );
              setShowPopover(false);
            }}
          >
            <div className="settings-menu-inner-icon report-icon">
              <ReportIcon />
            </div>
            <div className="settings-menu-inner-text">Report an issue</div>
          </div>
        </>
      ) : null}
      {/* Whiteboard Icon */}
      {!isMobileBrowser() &&
        (isHost || isCoHost) &&
        meetingFeatures?.whiteboard === 1 && (
          <>
            <div
              className="settings-menu-item"
              onClick={() => {
                setShowPopover(false);
                openWhiteboard();
              }}
            >
              <div className="settings-menu-inner-icon report-icon">
                <Whiteboard style={{ color: "#fff" }} />
              </div>
              <div className="settings-menu-inner-text">Whiteboard</div>
            </div>
          </>
        )}
      {/* Full Screen Button */}
      {!isMobileBrowser() && !isElectron() && (
        <div
          className="settings-menu-item"
          onClick={() => {
            toggleFullScreen();
            setShowPopover(false);
          }}
        >
          <div className="settings-menu-inner-icon full-screen-icon">
            {document.fullscreenElement ? <MinimizeIcon /> : <MaximizeIcon />}
          </div>
          <div className="settings-menu-inner-text">Full Screen</div>
        </div>
      )}
      {/* Participant Drawer for Mobile View */}
      {!isMobileBrowser() ? null : (
        <>
          <div
            onClick={() => {
              setOpenDrawer((prev) =>
                prev === DrawerState.PARTICIPANTS
                  ? DrawerState.NONE
                  : DrawerState.PARTICIPANTS
              );
            }}
            className="settings-menu-item mvt-options"
          >
            <div className="settings-menu-inner-icon">
              {openDrawer === DrawerState.PARTICIPANTS ? (
                <SvgParticipantBlueIcon />
              ) : (
                <SvgParticipantIcon />
              )}
            </div>
            {/* <div className="settings-menu-inner-text">Participants</div> */}
          </div>
        </>
      )}
      {/* Raise Hand for Mobile View */}
      {!isMobileBrowser() ? null : (
        <div
          onClick={() => setShowRaiseHand(!showRaiseHand)}
          className="settings-menu-item mvt-options"
        >
          <div className="settings-menu-inner-icon">
            {showRaiseHand ? <SvgRaiseHandOnIcon /> : <SvgRaiseHandIcon />}
          </div>
          {/* <div className="settings-menu-inner-text">Raise Hand</div> */}
        </div>
      )}


      {/* Settings Button */}
      {!isMobileBrowser() && meetingFeatures?.noise_cancellation === 1 && (
        <>
          <div
            className="settings-menu-item"
            onClick={() => {
              setIsSettingsModalOpen(!isSettingsModalOpen);
              setShowPopover(false);
            }}
          >
            <div className="settings-menu-inner-icon troubleshoot-icon">
              <SettingsIcon />
            </div>
            <div className="settings-menu-inner-text">Settings</div>
          </div>
          <SettingsModal
            isSettingsModalOpen={isSettingsModalOpen}
            setIsSettingsModalOpen={setIsSettingsModalOpen}
            room={room}
            meetingId={id}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            brightness={brightness}
            onBrightnessChange={onBrightnessChange}
            outputVolume={outputVolume}
            onOutputVolumeChange={onOutputVolumeChange}
            autoVideoOff={autoVideoOff}
            onAutoVideoOffChange={onAutoVideoOffChange}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={onAutoAudioOffChange}
            meetingFeatures={meetingFeatures}
            isHost={isHost}
            isCoHost={isCoHost}
            speakerDeviceId={speakerDeviceId}
            setSpeakerDeviceId={setSpeakerDeviceId}
            deviceIdAudio={deviceIdAudio}
            setDeviceIdAudio={setDeviceIdAudio}
          />
        </>
      )}
    </div>
  );

  const mobileContent = (
    <div className="primary-font setting-control-button setting-control-button-mobile">
      <div
        onClick={() => {
          setOpenDrawer((prev) =>
            prev === DrawerState.PARTICIPANTS
              ? DrawerState.NONE
              : DrawerState.PARTICIPANTS
          );
        }}
        className="settings-menu-item mvt-options"
      >
        <div className="settings-menu-inner-icon">
          {openDrawer === DrawerState.PARTICIPANTS ? (
            <SvgParticipantBlueIcon />
          ) : (
            <SvgParticipantIcon />
          )}
        </div>
        {/* <div className="settings-menu-inner-text">Participants</div> */}
      </div>
      <div
        onClick={() => setShowRaiseHand(!showRaiseHand)}
        className="settings-menu-item mvt-options"
      >
        <div className="settings-menu-inner-icon">
          {showRaiseHand ? <SvgRaiseHandOnIcon /> : <SvgRaiseHandIcon />}
        </div>
        {/* <div className="settings-menu-inner-text">Raise Hand</div> */}
      </div>
    </div>
  );

  return (
    <Popover
      content={isMobileBrowser() ? mobileContent : content}
      trigger="click"
      open={showPopover}
      onOpenChange={() => setShowPopover(!showPopover)}
      overlayInnerStyle={{
        backgroundColor: "#1e1e1e",
      }}
      overlayClassName="setting-control-button-popover"
    >
      {showPopover ? (
        <div className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons">
          <ThreeDotsIconBlue />
        </div>
      ) : (
        <div className="lk-button control-bar-button control-bar-button-icon lk-button-group-buttons">
          <ThreeDotsIcon />
        </div>
      )}
    </Popover>
  );
}
